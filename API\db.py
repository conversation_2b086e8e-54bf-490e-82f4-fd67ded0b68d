import os
import mysql.connector
from mysql.connector import pooling
from dotenv import load_dotenv

load_dotenv()

DB_CONFIG = {
    "host": os.getenv("DB_HOST", "127.0.0.1"),
    "port": int(os.getenv("DB_PORT", "3306")),
    "user": os.getenv("DB_USER"),
    "password": os.getenv("DB_PASS"),
    "database": os.getenv("DB_NAME", "sndb"),
    "autocommit": True,
}

# Pool koneksi biar efisien
pool = pooling.MySQLConnectionPool(pool_name="sndb_pool", pool_size=5, **DB_CONFIG)

def query_all(sql, params=None):
    conn = pool.get_connection()
    try:
        cur = conn.cursor(dictionary=True)
        cur.execute(sql, params or ())
        rows = cur.fetchall()
        cur.close()
        return rows
    finally:
        conn.close()

def query_one(sql, params=None):
    rows = query_all(sql, params)
    return rows[0] if rows else None
