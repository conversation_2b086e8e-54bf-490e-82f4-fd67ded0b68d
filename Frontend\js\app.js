// ===== Config =====
const API_BASE = "http://127.0.0.1:8000";
const AGENT_INTERVAL_SEC = 300; // match your Task Scheduler interval

// ===== UI Elements =====
const autoStatusEl = document.getElementById("autoStatus");
const countdownEl = document.getElementById("countdown");
const agentEtaEl = document.getElementById("agentEta");
const toggleAutoEl = document.getElementById("toggleAuto");
const toggleThemeEl = document.getElementById("toggleTheme");
const refreshNowEl = document.getElementById("refreshNow");

// ===== State =====
let chart = null;
let selectedDevice = null;
let autoOn = true;
let intervalSec = 30;
let counter = intervalSec;
let lastAgentRun = null;

// ===== Helpers =====
async function fetchJSON(url) {
  const r = await fetch(url);
  if (!r.ok) throw new Error(await r.text());
  return r.json();
}
const fetchSummary = () => fetchJSON(`${API_BASE}/summary`);
const fetchDevices = () => fetchJSON(`${API_BASE}/device-health`);
const fetchLogs = (id, days = 1) =>
  fetchJSON(`${API_BASE}/devices/${id}/logs?days=${days}`);

function formatTS(ts) {
  if (!ts) return "-";
  return new Date(ts.replace(" ", "T")).toLocaleString();
}
function newestTimestamp(devices) {
  const times = devices
    .map((d) => d.last_check)
    .filter(Boolean)
    .map((ts) => new Date(ts.replace(" ", "T")).getTime());
  if (!times.length) return null;
  return new Date(Math.max(...times));
}

// ===== Theme Toggle =====
function setThemeButtonLabel() {
  const isDark = document.documentElement.classList.contains("dark");
  if (toggleThemeEl) {
    toggleThemeEl.textContent = isDark ? "Light Mode" : "Dark Mode";
    console.log("Button label updated to:", toggleThemeEl.textContent);
  }
}

function toggleTheme() {
  try {
    const root = document.documentElement;
    console.log("🔄 === TOGGLE THEME START ===");
    console.log("📋 Before toggle - classList:", root.classList.toString());
    console.log(
      "🔍 Before toggle - contains dark:",
      root.classList.contains("dark")
    );

    const isDarkNow = root.classList.contains("dark");

    if (isDarkNow) {
      // Switch to light mode
      root.classList.remove("dark");
      localStorage.setItem("theme", "light");
      console.log("☀️ ✓ Switched to LIGHT mode");
    } else {
      // Switch to dark mode
      root.classList.add("dark");
      localStorage.setItem("theme", "dark");
      console.log("🌙 ✓ Switched to DARK mode");
    }

    console.log("📋 After toggle - classList:", root.classList.toString());
    console.log(
      "🔍 After toggle - contains dark:",
      root.classList.contains("dark")
    );
    console.log("✅ === TOGGLE THEME END ===");

    // Update button label
    setThemeButtonLabel();

    // Force browser to recalculate styles
    window.getComputedStyle(root).getPropertyValue("color");

    // Trigger a custom event to notify other parts of the app
    window.dispatchEvent(
      new CustomEvent("themeChanged", {
        detail: { isDark: root.classList.contains("dark") },
      })
    );
  } catch (error) {
    console.error("❌ Error toggling theme:", error);
  }
}

// Make functions available globally
window.toggleTheme = toggleTheme;

function resetTheme() {
  try {
    console.log("🔄 Resetting theme...");
    localStorage.removeItem("theme");
    document.documentElement.classList.remove("dark");
    document.documentElement.className = "h-full";
    console.log(
      "✅ Theme reset complete - classList:",
      document.documentElement.classList.toString()
    );
    setThemeButtonLabel();

    // Trigger theme changed event
    window.dispatchEvent(
      new CustomEvent("themeChanged", {
        detail: { isDark: false },
      })
    );
  } catch (error) {
    console.error("❌ Error resetting theme:", error);
  }
}

// Make reset function available globally
window.resetTheme = resetTheme;

// Initialize theme toggle
function initThemeToggle() {
  console.log("🔧 Initializing theme toggle...");

  // Try multiple times to ensure DOM is ready
  let attempts = 0;
  const maxAttempts = 10;

  function tryInit() {
    attempts++;
    console.log(`🔄 Attempt ${attempts} to initialize theme toggle...`);

    const toggleBtn = document.getElementById("toggleTheme");
    const resetBtn = document.getElementById("resetTheme");

    if (toggleBtn) {
      console.log("✅ Theme toggle button found!");

      // Test if button is clickable
      console.log("🔍 Button properties:", {
        id: toggleBtn.id,
        className: toggleBtn.className,
        textContent: toggleBtn.textContent,
        onclick: toggleBtn.onclick,
      });

      // Remove any existing event listeners and add new ones
      toggleBtn.removeEventListener("click", toggleTheme);
      toggleBtn.addEventListener("click", function (e) {
        console.log("🖱️ Button clicked via event listener!");
        e.preventDefault();
        toggleTheme();
      });

      // Also ensure onclick works
      toggleBtn.onclick = function (e) {
        console.log("🖱️ Button clicked via onclick!");
        e.preventDefault();
        toggleTheme();
      };

      setThemeButtonLabel(); // set label on first load

      // Visual feedback
      toggleBtn.style.cursor = "pointer";
      toggleBtn.style.border = "2px solid transparent";

      console.log("✅ Theme toggle initialized successfully");
      return true; // Success
    } else {
      console.warn(`⚠️ Theme toggle button not found on attempt ${attempts}`);

      if (attempts < maxAttempts) {
        setTimeout(tryInit, 200);
      } else {
        console.error(
          "❌ Failed to find theme toggle button after all attempts"
        );
      }
      return false;
    }
  }

  // Start initialization
  tryInit();
}

// ===== Renderers =====
function renderSummary(data) {
  const container = document.getElementById("summary");
  container.innerHTML = "";
  const order = ["ONLINE", "OFFLINE", "UNKNOWN"];
  const map = Object.fromEntries(
    data.status_counts.map((s) => [s.status, s.total])
  );
  order.forEach((status) => {
    const total = map[status] ?? 0;
    const card = document.createElement("div");
    const bar =
      status === "ONLINE"
        ? "bg-green-500"
        : status === "OFFLINE"
        ? "bg-red-500"
        : "bg-gray-500";
    card.className =
      "rounded-xl shadow p-4 text-center bg-white dark:bg-slate-800";
    card.innerHTML = `
      <div class="text-sm opacity-70">${status}</div>
      <div class="mt-2 text-3xl font-extrabold">${total}</div>
      <div class="mt-3 h-2 rounded ${bar}"></div>
    `;
    container.appendChild(card);
  });
}

function renderDevices(devices) {
  const tbody = document.querySelector("#deviceTable tbody");
  tbody.innerHTML = "";
  devices.forEach((d) => {
    const tr = document.createElement("tr");
    tr.className = "hover:bg-gray-50 dark:hover:bg-slate-700/40";
    tr.innerHTML = `
      <td class="p-2">${d.name}</td>
      <td class="p-2">${d.ip_address}</td>
      <td class="p-2">
        <span class="px-2 py-1 rounded text-white text-xs ${
          d.last_status === "ONLINE"
            ? "bg-green-600"
            : d.last_status === "OFFLINE"
            ? "bg-red-600"
            : "bg-gray-600"
        }">${d.last_status}</span>
      </td>
      <td class="p-2">${formatTS(d.last_check)}</td>
      <td class="p-2">
        <button class="px-3 py-1 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm"
          data-id="${d.id}" data-name="${d.name}">
          View Logs
        </button>
      </td>
    `;
    tbody.appendChild(tr);
  });

  tbody.querySelectorAll("button").forEach((btn) => {
    btn.addEventListener("click", () => {
      selectedDevice = { id: Number(btn.dataset.id), name: btn.dataset.name };
      loadChart(selectedDevice.id, selectedDevice.name);
      document.getElementById(
        "chartTitle"
      ).innerText = `Logs for ${selectedDevice.name}`;
    });
  });

  if (!selectedDevice && devices.length) {
    selectedDevice = { id: devices[0].id, name: devices[0].name };
    loadChart(selectedDevice.id, selectedDevice.name);
    document.getElementById(
      "chartTitle"
    ).innerText = `Logs for ${selectedDevice.name}`;
  }
}

async function loadChart(deviceId, name) {
  const logs = await fetchLogs(deviceId, 1);
  const ctx = document.getElementById("logChart").getContext("2d");
  const labels = logs.map((l) => formatTS(l.checked_at));
  const data = logs.map((l) => l.latency_ms);
  if (chart) chart.destroy();
  chart = new Chart(ctx, {
    type: "line",
    data: {
      labels,
      datasets: [
        {
          label: `Latency (ms) - ${name}`,
          data,
          borderWidth: 2,
          tension: 0.25,
          pointRadius: 0,
        },
      ],
    },
    options: {
      responsive: true,
      scales: {
        x: { title: { display: true, text: "Time" } },
        y: {
          title: { display: true, text: "Latency (ms)" },
          beginAtZero: true,
        },
      },
    },
  });
}

// ===== Agent ETA =====
function updateAgentEta() {
  if (!agentEtaEl) return;
  if (!lastAgentRun) {
    agentEtaEl.textContent = "—";
    return;
  }
  const elapsed = Math.floor((Date.now() - lastAgentRun.getTime()) / 1000);
  let eta = AGENT_INTERVAL_SEC - (elapsed % AGENT_INTERVAL_SEC);
  if (eta < 0) eta = 0;
  agentEtaEl.textContent = String(eta);
}

// ===== Refresh =====
async function refreshAll({ keepChart = true } = {}) {
  try {
    const [summary, devices] = await Promise.all([
      fetchSummary(),
      fetchDevices(),
    ]);
    renderSummary(summary);
    renderDevices(devices);
    lastAgentRun = newestTimestamp(devices);
    updateAgentEta();
    if (!keepChart && selectedDevice) {
      await loadChart(selectedDevice.id, selectedDevice.name);
    }
  } catch (e) {
    console.error(e);
  }
}

// ===== Controls =====
toggleAutoEl?.addEventListener("click", () => {
  autoOn = !autoOn;
  autoStatusEl.textContent = autoOn ? "ON" : "OFF";
  toggleAutoEl.textContent = autoOn ? "Pause" : "Resume";
  if (autoOn) counter = intervalSec;
});
refreshNowEl?.addEventListener("click", async () => {
  await refreshAll({ keepChart: false });
  counter = intervalSec;
});

// ===== 1s UI timer =====
setInterval(async () => {
  if (!autoOn) return;
  counter -= 1;
  countdownEl.textContent = String(counter);
  updateAgentEta();
  if (counter <= 0) {
    await refreshAll({ keepChart: false });
    counter = intervalSec;
  }
}, 1000);

// ===== Init =====
// Since script is loaded with defer, DOM is already ready
console.log("🚀 App initialization starting...");
console.log("🔧 Available functions:", {
  toggleTheme: typeof window.toggleTheme,
  resetTheme: typeof window.resetTheme,
  setThemeButtonLabel: typeof setThemeButtonLabel,
});

// Add test functions to window for manual testing
window.testToggle = function () {
  console.log("🧪 Manual test toggle called");
  if (window.toggleTheme) {
    window.toggleTheme();
  } else {
    console.error("❌ toggleTheme function not available");
  }
};

window.testReset = function () {
  console.log("🧪 Manual test reset called");
  if (window.resetTheme) {
    window.resetTheme();
  } else {
    console.error("❌ resetTheme function not available");
  }
};

console.log("🧪 Test functions added to window: testToggle(), testReset()");

initThemeToggle();
refreshAll();

console.log("✅ App initialization complete");

// Final check after a short delay
setTimeout(() => {
  const toggleBtn = document.getElementById("toggleTheme");
  console.log(
    "🔍 Final check - toggle button:",
    toggleBtn ? "FOUND" : "NOT FOUND"
  );
  if (toggleBtn) {
    console.log("🔍 Button details:", {
      id: toggleBtn.id,
      textContent: toggleBtn.textContent,
      hasOnclick: !!toggleBtn.onclick,
      hasEventListener: toggleBtn._hasEventListener || "unknown",
    });
  }
}, 1000);
