<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Debug Theme Toggle</title>
  
  <style>
    /* Custom CSS for debugging */
    :root {
      --bg-color: #f3f4f6;
      --text-color: #111827;
      --card-bg: #ffffff;
    }
    
    .dark {
      --bg-color: #111827;
      --text-color: #f9fafb;
      --card-bg: #1f2937;
    }
    
    body {
      background-color: var(--bg-color);
      color: var(--text-color);
      transition: background-color 0.3s, color 0.3s;
      font-family: system-ui, -apple-system, sans-serif;
      margin: 0;
      padding: 20px;
    }
    
    .card {
      background-color: var(--card-bg);
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: background-color 0.3s;
    }
    
    button {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      margin: 5px;
      transition: all 0.2s;
    }
    
    .toggle-btn {
      background: #374151;
      color: white;
    }
    
    .dark .toggle-btn {
      background: #e5e7eb;
      color: #111827;
    }
    
    .reset-btn {
      background: #dc2626;
      color: white;
    }
    
    .test-btn {
      background: #2563eb;
      color: white;
    }
  </style>
</head>

<body>
  <h1>🔧 Debug Theme Toggle</h1>
  
  <div class="card">
    <h2>Controls</h2>
    <button id="toggleTheme" class="toggle-btn">Toggle Theme</button>
    <button id="resetTheme" class="reset-btn">Reset Theme</button>
    <button id="testTailwind" class="test-btn">Test Tailwind</button>
  </div>
  
  <div class="card">
    <h2>Debug Information</h2>
    <div id="debugInfo"></div>
  </div>
  
  <div class="card">
    <h2>Test Content</h2>
    <p>This is test content to verify theme switching works correctly.</p>
    <div style="padding: 10px; background: rgba(0,0,0,0.1); border-radius: 4px; margin: 10px 0;">
      Nested content with semi-transparent background
    </div>
  </div>

  <script>
    const toggleThemeEl = document.getElementById('toggleTheme');
    const resetThemeEl = document.getElementById('resetTheme');
    const testTailwindEl = document.getElementById('testTailwind');
    const debugInfoEl = document.getElementById('debugInfo');

    function updateDebugInfo() {
      const root = document.documentElement;
      const isDark = root.classList.contains('dark');
      const savedTheme = localStorage.getItem('theme');
      const computedStyle = getComputedStyle(document.body);
      
      debugInfoEl.innerHTML = `
        <div><strong>HTML classList:</strong> "${root.className}"</div>
        <div><strong>Contains 'dark':</strong> ${isDark}</div>
        <div><strong>localStorage theme:</strong> ${savedTheme || 'null'}</div>
        <div><strong>Computed bg-color:</strong> ${computedStyle.backgroundColor}</div>
        <div><strong>Computed color:</strong> ${computedStyle.color}</div>
        <div><strong>CSS var --bg-color:</strong> ${computedStyle.getPropertyValue('--bg-color')}</div>
        <div><strong>CSS var --text-color:</strong> ${computedStyle.getPropertyValue('--text-color')}</div>
        <div><strong>Button text:</strong> ${toggleThemeEl.textContent}</div>
      `;
    }

    function setThemeButtonLabel() {
      const isDark = document.documentElement.classList.contains('dark');
      toggleThemeEl.textContent = isDark ? 'Light Mode' : 'Dark Mode';
    }

    function toggleTheme() {
      console.log('=== TOGGLE THEME START ===');
      const root = document.documentElement;
      
      console.log('Before - classList:', root.classList.toString());
      console.log('Before - contains dark:', root.classList.contains('dark'));
      
      const isDarkNow = root.classList.contains('dark');
      
      if (isDarkNow) {
        console.log('Removing dark class...');
        root.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      } else {
        console.log('Adding dark class...');
        root.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      }
      
      console.log('After - classList:', root.classList.toString());
      console.log('After - contains dark:', root.classList.contains('dark'));
      console.log('=== TOGGLE THEME END ===');
      
      setThemeButtonLabel();
      updateDebugInfo();
    }

    function resetTheme() {
      console.log('=== RESET THEME ===');
      const root = document.documentElement;
      localStorage.removeItem('theme');
      root.className = '';
      console.log('Reset complete - classList:', root.classList.toString());
      setThemeButtonLabel();
      updateDebugInfo();
    }

    function testTailwind() {
      console.log('=== TESTING TAILWIND ===');
      
      // Create a test element with Tailwind classes
      const testEl = document.createElement('div');
      testEl.className = 'bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-100 p-4 rounded';
      testEl.textContent = 'Tailwind Test Element';
      testEl.style.position = 'fixed';
      testEl.style.top = '10px';
      testEl.style.right = '10px';
      testEl.style.zIndex = '9999';
      
      document.body.appendChild(testEl);
      
      setTimeout(() => {
        document.body.removeChild(testEl);
      }, 3000);
      
      console.log('Tailwind test element created and will be removed in 3 seconds');
    }

    // Initialize
    if (toggleThemeEl) {
      toggleThemeEl.addEventListener('click', toggleTheme);
    }
    
    if (resetThemeEl) {
      resetThemeEl.addEventListener('click', resetTheme);
    }
    
    if (testTailwindEl) {
      testTailwindEl.addEventListener('click', testTailwind);
    }

    // Apply saved theme on load
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    }

    setThemeButtonLabel();
    updateDebugInfo();
    
    // Update debug info every second
    setInterval(updateDebugInfo, 1000);
  </script>
</body>
</html>
