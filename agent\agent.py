import mysql.connector
from ping3 import ping
import statistics
from datetime import datetime

# Konfigurasi koneksi database
db_config = {
    "host": "127.0.0.1",
    "user": "sndb_user",
    "password": "reiatsu#1212",  # ganti se<PERSON>ai yang kamu pakai
    "database": "sndb"
}

# Ambil daftar device dari tabel devices
def get_devices():
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)
    cursor.execute("SELECT id, name, ip_address FROM devices WHERE is_active=1")
    devices = cursor.fetchall()
    cursor.close()
    conn.close()
    return devices

# Lakukan ping dan hitung latency/jitter/packet loss
def check_device(ip, count=4):
    latencies = []
    for i in range(count):
        try:
            delay = ping(ip, timeout=2)  # hasil dalam detik
            if delay is not None:
                latencies.append(delay * 1000)  # konversi ke ms
        except Exception:
            pass

    if len(latencies) == 0:
        return None  # gagal total

    avg_latency = round(statistics.mean(latencies), 2)
    jitter = round(statistics.pstdev(latencies), 2) if len(latencies) > 1 else 0
    packet_loss = round(((count - len(latencies)) / count) * 100, 2)

    return {
        "latency_ms": avg_latency,
        "jitter_ms": jitter,
        "packet_loss_pct": packet_loss,
        "result": "SUCCESS" if packet_loss < 100 else "TIMEOUT"
    }

# Simpan hasil ke tabel network_checks
def save_result(device_id, result_data):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    sql = """
        INSERT INTO network_checks
        (device_id, latency_ms, jitter_ms, packet_loss_pct, result, checked_at)
        VALUES (%s, %s, %s, %s, %s, %s)
    """
    values = (
        device_id,
        result_data.get("latency_ms"),
        result_data.get("jitter_ms"),
        result_data.get("packet_loss_pct"),
        result_data.get("result"),
        datetime.now()
    )
    cursor.execute(sql, values)
    conn.commit()
    cursor.close()
    conn.close()

# Main process
if __name__ == "__main__":
    devices = get_devices()
    for dev in devices:
        print(f"Checking {dev['name']} ({dev['ip_address']})...")
        result = check_device(dev["ip_address"])
        if result:
            print("Result:", result)
            save_result(dev["id"], result)
        else:
            print("Unreachable")
            save_result(dev["id"], {
                "latency_ms": None,
                "jitter_ms": None,
                "packet_loss_pct": 100.0,
                "result": "TIMEOUT"
            })
