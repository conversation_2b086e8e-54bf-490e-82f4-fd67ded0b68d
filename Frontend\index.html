<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <meta charset="UTF-8" />
    <title>Smart Network Dashboard</title>

    <!-- 1) Set Tailwind config BEFORE loading the CDN -->
    <script>
      // Configure Tailwind for dark mode
      window.tailwind = window.tailwind || {};
      window.tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {},
        },
      };
    </script>
    <!-- 2) Load Tailwind AFTER the config -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 3) Apply initial theme BEFORE first paint -->
    <script>
      (function () {
        try {
          // Clear any existing dark class first
          document.documentElement.classList.remove("dark");

          const saved = localStorage.getItem("theme"); // 'dark' | 'light' | null
          console.log("Initial theme setup - saved theme:", saved);

          if (saved === "dark") {
            document.documentElement.classList.add("dark");
            console.log("Applied dark theme from localStorage");
          } else if (saved === "light") {
            // Keep light (no dark class)
            console.log("Applied light theme from localStorage");
          } else {
            // No saved preference, use system preference
            const prefersDark = window.matchMedia(
              "(prefers-color-scheme: dark)"
            ).matches;
            if (prefersDark) {
              document.documentElement.classList.add("dark");
              localStorage.setItem("theme", "dark");
              console.log("Applied dark theme from system preference");
            } else {
              localStorage.setItem("theme", "light");
              console.log("Applied light theme from system preference");
            }
          }

          console.log(
            "Final classList after initial setup:",
            document.documentElement.classList.toString()
          );
        } catch (error) {
          console.error("Error setting initial theme:", error);
        }
      })();
    </script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- App JS (defer so DOM exists) -->
    <script src="js/app.js" defer></script>

    <!-- Direct theme initialization -->
    <script defer>
      document.addEventListener("DOMContentLoaded", function () {
        console.log("🔧 Direct theme initialization...");

        // Set correct button label based on current theme
        const toggleBtn = document.getElementById("toggleTheme");
        if (toggleBtn) {
          const isDark = document.documentElement.classList.contains("dark");
          toggleBtn.textContent = isDark ? "Light Mode" : "Dark Mode";
          console.log("🏷️ Button label set to:", toggleBtn.textContent);
        }

        console.log("✅ Direct theme initialization complete");
      });
    </script>
  </head>

  <body
    class="h-full bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300 ease-in-out">
    <div class="container mx-auto p-6 space-y-6">
      <!-- Header -->
      <div
        class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
        <h1 class="text-2xl md:text-3xl font-bold">
          📡 Smart Network Dashboard
        </h1>

        <div class="flex items-center gap-3">
          <div class="text-sm opacity-70">
            Auto-refresh:
            <span id="autoStatus" class="font-semibold">ON</span> · Next UI
            refresh in <span id="countdown" class="font-mono">30</span>s · Next
            agent in <span id="agentEta" class="font-mono">—</span>s
          </div>

          <button
            id="toggleAuto"
            class="px-3 py-1 rounded-lg bg-amber-500 hover:bg-amber-600 text-white text-sm">
            Pause
          </button>

          <!-- Direct inline theme toggle -->
          <button
            id="toggleTheme"
            onclick="
              console.log('🖱️ Direct toggle clicked!');
              const root = document.documentElement;
              const isDark = root.classList.contains('dark');
              console.log('Current state - isDark:', isDark);

              if (isDark) {
                root.classList.remove('dark');
                localStorage.setItem('theme', 'light');
                this.textContent = 'Dark Mode';
                console.log('☀️ Switched to LIGHT mode');
              } else {
                root.classList.add('dark');
                localStorage.setItem('theme', 'dark');
                this.textContent = 'Light Mode';
                console.log('🌙 Switched to DARK mode');
              }

              console.log('New classList:', root.classList.toString());
            "
            class="px-3 py-1 rounded-lg bg-slate-800 hover:bg-slate-700 text-white text-sm dark:bg-slate-200 dark:hover:bg-slate-300 dark:text-slate-900 transition-all duration-200 ease-in-out cursor-pointer">
            Toggle Dark
          </button>

          <button
            id="resetTheme"
            onclick="
              console.log('🖱️ Direct reset clicked!');
              localStorage.removeItem('theme');
              document.documentElement.classList.remove('dark');
              document.documentElement.className = 'h-full';
              document.getElementById('toggleTheme').textContent = 'Dark Mode';
              console.log('✅ Theme reset to light mode');
            "
            class="px-3 py-1 rounded-lg bg-red-600 hover:bg-red-700 text-white text-sm transition-all duration-200 ease-in-out cursor-pointer">
            Reset
          </button>
        </div>
      </div>

      <!-- Summary -->
      <div id="summary" class="grid grid-cols-1 md:grid-cols-3 gap-4"></div>

      <!-- Devices table -->
      <div class="bg-white dark:bg-slate-800 rounded-xl shadow p-4">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-semibold">Devices</h2>
          <button
            id="refreshNow"
            class="px-3 py-1 rounded-lg bg-blue-600 hover:bg-blue-700 text-white text-sm">
            Refresh Now
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="w-full border-collapse" id="deviceTable">
            <thead>
              <tr class="bg-gray-200 dark:bg-slate-700">
                <th class="p-2 text-left">Name</th>
                <th class="p-2 text-left">IP</th>
                <th class="p-2 text-left">Status</th>
                <th class="p-2 text-left">Last Check</th>
                <th class="p-2 text-left">Action</th>
              </tr>
            </thead>
            <tbody
              class="divide-y divide-gray-200 dark:divide-slate-700"></tbody>
          </table>
        </div>
      </div>

      <!-- Chart -->
      <div class="bg-white dark:bg-slate-800 rounded-xl shadow p-4">
        <h2 class="text-xl font-semibold mb-4" id="chartTitle">Device Logs</h2>
        <canvas id="logChart" height="100"></canvas>
      </div>
    </div>
  </body>
</html>
