<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
  <meta charset="UTF-8" />
  <title>Simple Theme Test</title>

  <!-- Tailwind CSS -->
  <script>
    window.tailwind = window.tailwind || {};
    window.tailwind.config = { 
      darkMode: 'class'
    };
  </script>
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Initial theme setup -->
  <script>
    console.log("🔧 Setting up initial theme...");
    
    // Clear any existing dark class first
    document.documentElement.classList.remove("dark");
    
    const saved = localStorage.getItem("theme");
    console.log("💾 Saved theme:", saved);
    
    if (saved === "dark") {
      document.documentElement.classList.add("dark");
      console.log("🌙 Applied dark theme");
    } else {
      console.log("☀️ Applied light theme");
    }
    
    console.log("📋 Initial classList:", document.documentElement.classList.toString());
  </script>
</head>

<body class="h-full bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300 ease-in-out">
  <div class="container mx-auto p-6 space-y-6">
    
    <h1 class="text-3xl font-bold">🧪 Simple Theme Test</h1>

    <div class="flex gap-4">
      <button
        onclick="testToggle()"
        class="px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700 text-white">
        Toggle Theme
      </button>
      
      <button
        onclick="testReset()"
        class="px-4 py-2 rounded-lg bg-red-600 hover:bg-red-700 text-white">
        Reset Theme
      </button>
      
      <button
        onclick="testStatus()"
        class="px-4 py-2 rounded-lg bg-green-600 hover:bg-green-700 text-white">
        Check Status
      </button>
    </div>

    <div class="bg-white dark:bg-slate-800 rounded-xl shadow p-6 transition-colors duration-300">
      <h2 class="text-xl font-semibold mb-4">Test Card</h2>
      <p class="text-gray-600 dark:text-gray-300">
        This card should change colors when you toggle the theme.
      </p>
      
      <div id="status" class="mt-4 p-3 bg-gray-100 dark:bg-slate-700 rounded-lg font-mono text-sm">
        Status will appear here...
      </div>
    </div>

  </div>

  <script>
    function updateStatus() {
      const root = document.documentElement;
      const isDark = root.classList.contains('dark');
      const saved = localStorage.getItem('theme');
      
      document.getElementById('status').innerHTML = `
        <div>🔍 Contains 'dark': ${isDark}</div>
        <div>📋 Full classList: "${root.className}"</div>
        <div>💾 localStorage: ${saved || 'null'}</div>
        <div>🎨 Current mode: ${isDark ? 'DARK' : 'LIGHT'}</div>
      `;
    }

    function testToggle() {
      console.log("🔄 === MANUAL TOGGLE TEST ===");
      const root = document.documentElement;
      
      console.log("📋 Before - classList:", root.classList.toString());
      const isDarkNow = root.classList.contains('dark');
      console.log("🔍 Before - contains dark:", isDarkNow);
      
      if (isDarkNow) {
        root.classList.remove('dark');
        localStorage.setItem('theme', 'light');
        console.log("☀️ Switched to LIGHT");
      } else {
        root.classList.add('dark');
        localStorage.setItem('theme', 'dark');
        console.log("🌙 Switched to DARK");
      }
      
      console.log("📋 After - classList:", root.classList.toString());
      console.log("🔍 After - contains dark:", root.classList.contains('dark'));
      console.log("✅ === TOGGLE COMPLETE ===");
      
      updateStatus();
    }

    function testReset() {
      console.log("🔄 === RESET TEST ===");
      localStorage.removeItem('theme');
      document.documentElement.classList.remove('dark');
      document.documentElement.className = 'h-full';
      console.log("✅ Reset complete");
      updateStatus();
    }

    function testStatus() {
      console.log("📊 === STATUS CHECK ===");
      updateStatus();
      
      const root = document.documentElement;
      console.log("📋 classList:", root.classList.toString());
      console.log("🔍 contains dark:", root.classList.contains('dark'));
      console.log("💾 localStorage:", localStorage.getItem('theme'));
      
      // Test computed styles
      const computedStyle = getComputedStyle(document.body);
      console.log("🎨 Computed bg-color:", computedStyle.backgroundColor);
      console.log("🎨 Computed color:", computedStyle.color);
    }

    // Initialize
    updateStatus();
    
    // Auto-update status every 2 seconds
    setInterval(updateStatus, 2000);
    
    console.log("✅ Simple test page loaded");
  </script>
</body>
</html>
