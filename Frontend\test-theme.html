<!DOCTYPE html>
<html lang="en" class="h-full">
  <head>
    <meta charset="UTF-8" />
    <title>Theme Toggle Test</title>

    <!-- 1) Set Tailwind config BEFORE loading the CDN -->
    <script>
      // Configure Tailwind for dark mode
      window.tailwind = window.tailwind || {};
      window.tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {},
        },
      };
    </script>
    <!-- 2) Load Tailwind AFTER the config -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 3) Apply initial theme BEFORE first paint -->
    <script>
      (function () {
        try {
          const saved = localStorage.getItem("theme"); // 'dark' | 'light' | null
          const prefersDark = window.matchMedia(
            "(prefers-color-scheme: dark)"
          ).matches;
          const useDark = saved ? saved === "dark" : prefersDark;

          if (useDark) {
            document.documentElement.classList.add("dark");
          } else {
            document.documentElement.classList.remove("dark");
          }

          // Store the initial theme if not already stored
          if (!saved) {
            localStorage.setItem("theme", useDark ? "dark" : "light");
          }
        } catch (error) {
          console.error("Error setting initial theme:", error);
        }
      })();
    </script>
  </head>

  <body
    class="h-full bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100 transition-colors duration-300 ease-in-out">
    <div class="container mx-auto p-6 space-y-6">
      <!-- Header -->
      <div
        class="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
        <h1 class="text-2xl md:text-3xl font-bold">🧪 Theme Toggle Test</h1>

        <div class="flex items-center gap-3">
          <button
            id="toggleTheme"
            class="px-4 py-2 rounded-lg bg-slate-800 hover:bg-slate-700 text-white text-sm dark:bg-slate-200 dark:hover:bg-slate-300 dark:text-slate-900 transition-all duration-200 ease-in-out cursor-pointer">
            Toggle Theme
          </button>
          <button
            id="resetTheme"
            class="px-4 py-2 rounded-lg bg-red-600 hover:bg-red-700 text-white text-sm transition-all duration-200 ease-in-out cursor-pointer">
            Reset
          </button>
        </div>
      </div>

      <!-- Test Content -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Card 1 -->
        <div
          class="bg-white dark:bg-slate-800 rounded-xl shadow p-6 transition-colors duration-300">
          <h2 class="text-xl font-semibold mb-4">Test Card 1</h2>
          <p class="text-gray-600 dark:text-gray-300">
            This is a test card to verify that the dark mode toggle is working
            correctly. The background should change from white to dark slate,
            and text colors should adapt accordingly.
          </p>
          <div
            class="mt-4 p-3 bg-gray-100 dark:bg-slate-700 rounded-lg transition-colors duration-300">
            <p class="text-sm">Nested content with different background</p>
          </div>
        </div>

        <!-- Card 2 -->
        <div
          class="bg-white dark:bg-slate-800 rounded-xl shadow p-6 transition-colors duration-300">
          <h2 class="text-xl font-semibold mb-4">Test Card 2</h2>
          <div class="space-y-2">
            <div
              class="px-3 py-2 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded transition-colors duration-300">
              Success state
            </div>
            <div
              class="px-3 py-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded transition-colors duration-300">
              Error state
            </div>
            <div
              class="px-3 py-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded transition-colors duration-300">
              Info state
            </div>
          </div>
        </div>
      </div>

      <!-- Status Display -->
      <div
        class="bg-white dark:bg-slate-800 rounded-xl shadow p-4 transition-colors duration-300">
        <h3 class="text-lg font-semibold mb-2">Current Theme Status</h3>
        <div id="themeStatus" class="font-mono text-sm"></div>
      </div>
    </div>

    <script>
      // Theme toggle functionality
      const toggleThemeEl = document.getElementById("toggleTheme");
      const themeStatusEl = document.getElementById("themeStatus");

      function updateThemeStatus() {
        const isDark = document.documentElement.classList.contains("dark");
        const savedTheme = localStorage.getItem("theme");

        if (themeStatusEl) {
          themeStatusEl.innerHTML = `
          <div>Current mode: <span class="font-bold">${
            isDark ? "Dark" : "Light"
          }</span></div>
          <div>Saved in localStorage: <span class="font-bold">${
            savedTheme || "null"
          }</span></div>
          <div>HTML class list: <span class="font-bold">${
            document.documentElement.className
          }</span></div>
        `;
        }
      }

      function setThemeButtonLabel() {
        const isDark = document.documentElement.classList.contains("dark");
        if (toggleThemeEl) {
          toggleThemeEl.textContent = isDark ? "Light Mode" : "Dark Mode";
        }
      }

      function toggleTheme() {
        try {
          const root = document.documentElement;
          console.log("Before toggle - classList:", root.classList.toString());
          console.log(
            "Before toggle - contains dark:",
            root.classList.contains("dark")
          );

          const isDarkNow = root.classList.contains("dark");

          // Force clear and set the class
          root.classList.remove("dark");

          if (!isDarkNow) {
            root.classList.add("dark");
            localStorage.setItem("theme", "dark");
            console.log("Switched to DARK mode");
          } else {
            localStorage.setItem("theme", "light");
            console.log("Switched to LIGHT mode");
          }

          console.log("After toggle - classList:", root.classList.toString());
          console.log(
            "After toggle - contains dark:",
            root.classList.contains("dark")
          );

          setThemeButtonLabel();
          updateThemeStatus();

          // Force a repaint
          root.style.display = "none";
          root.offsetHeight; // trigger reflow
          root.style.display = "";
        } catch (error) {
          console.error("Error toggling theme:", error);
        }
      }

      function resetTheme() {
        try {
          console.log("Resetting theme...");
          localStorage.removeItem("theme");
          document.documentElement.classList.remove("dark");
          document.documentElement.className = "h-full";
          console.log("Theme reset complete");
          setThemeButtonLabel();
          updateThemeStatus();
        } catch (error) {
          console.error("Error resetting theme:", error);
        }
      }

      // Initialize
      if (toggleThemeEl) {
        toggleThemeEl.addEventListener("click", toggleTheme);
        setThemeButtonLabel();
        updateThemeStatus();
      }

      const resetThemeEl = document.getElementById("resetTheme");
      if (resetThemeEl) {
        resetThemeEl.addEventListener("click", resetTheme);
      }

      // Update status every second to monitor changes
      setInterval(updateThemeStatus, 1000);
    </script>
  </body>
</html>
