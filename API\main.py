from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
from datetime import datetime, timedelta
from db import query_all, query_one

app = FastAPI(title="Smart Network Dashboard API", version="1.0.0")

# Izinkan akses dari FE nanti
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # produksi: ganti ke domain FE kamu
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
def health():
    # uji ping ke DB pakai query ringan
    row = query_one("SELECT 1 AS ok")
    return {"status": "ok", "db": bool(row and row["ok"] == 1)}

# ======== DEVICES ========
@app.get("/devices")
def list_devices(include_inactive: bool = False):
    sql = "SELECT id, name, ip_address, type, location, is_active, last_status, last_check FROM devices"
    if not include_inactive:
        sql += " WHERE is_active = 1"
    sql += " ORDER BY is_active DESC, last_status DESC, name ASC"
    return query_all(sql)

@app.get("/devices/{device_id}")
def get_device(device_id: int):
    row = query_one("""SELECT id, name, ip_address, type, location, is_active, last_status, last_check
                       FROM devices WHERE id=%s""", (device_id,))
    if not row:
        raise HTTPException(404, "Device not found")
    return row

# ======== SNAPSHOT TERBARU (VIEW) ========
@app.get("/device-health")
def device_health():
    return query_all("""SELECT * FROM v_device_health
                        ORDER BY is_active DESC, last_status DESC, name ASC""")

# ======== LOGS ========
@app.get("/devices/{device_id}/logs")
def device_logs(
    device_id: int,
    days: int = Query(7, ge=1, le=90),
    limit: int = Query(500, ge=1, le=5000)
):
    since = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")
    rows = query_all(
        """SELECT checked_at, latency_ms, jitter_ms, packet_loss_pct, result
           FROM network_checks
           WHERE device_id=%s AND checked_at >= %s
           ORDER BY checked_at ASC
           LIMIT %s""",
        (device_id, since, limit),
    )
    return rows

# ======== RINGKASAN ========
@app.get("/summary")
def summary():
    status_counts = query_all("SELECT last_status AS status, COUNT(*) AS total FROM devices GROUP BY last_status")
    latest = query_all("""SELECT d.id, d.name, nc.checked_at, nc.result, nc.latency_ms
                          FROM devices d
                          LEFT JOIN network_checks nc
                            ON nc.id = (
                              SELECT x.id FROM network_checks x
                              WHERE x.device_id = d.id
                              ORDER BY x.checked_at DESC LIMIT 1
                            )
                          ORDER BY nc.checked_at DESC LIMIT 10""")
    return {"status_counts": status_counts, "latest_checks": latest}
