<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Minimal Theme Test</title>
  
  <!-- Tailwind CSS -->
  <script>
    window.tailwind = window.tailwind || {};
    window.tailwind.config = { darkMode: 'class' };
  </script>
  <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="min-h-screen bg-white dark:bg-gray-900 text-black dark:text-white transition-colors duration-300">
  <div class="p-8">
    <h1 class="text-3xl font-bold mb-6">🧪 Minimal Theme Test</h1>
    
    <div class="mb-6">
      <button 
        onclick="toggleThemeMinimal()"
        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg mr-4">
        Toggle Theme
      </button>
      
      <button 
        onclick="resetThemeMinimal()"
        class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg">
        Reset
      </button>
    </div>
    
    <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4">
      <h2 class="text-xl font-semibold mb-2">Test Card</h2>
      <p class="text-gray-700 dark:text-gray-300">
        This card should change colors when you toggle the theme.
      </p>
    </div>
    
    <div id="status" class="bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg font-mono text-sm">
      Status will appear here...
    </div>
  </div>

  <script>
    function updateStatus() {
      const root = document.documentElement;
      const isDark = root.classList.contains('dark');
      const saved = localStorage.getItem('theme');
      
      document.getElementById('status').innerHTML = `
        <div><strong>HTML classList:</strong> "${root.className}"</div>
        <div><strong>Contains 'dark':</strong> ${isDark}</div>
        <div><strong>localStorage:</strong> ${saved || 'null'}</div>
        <div><strong>Current mode:</strong> ${isDark ? 'DARK' : 'LIGHT'}</div>
      `;
    }

    function toggleThemeMinimal() {
      console.log('=== MINIMAL TOGGLE START ===');
      const root = document.documentElement;
      
      console.log('Before - classList:', root.classList.toString());
      const isDark = root.classList.contains('dark');
      console.log('Before - contains dark:', isDark);
      
      if (isDark) {
        root.classList.remove('dark');
        localStorage.setItem('theme', 'light');
        console.log('Switched to LIGHT');
      } else {
        root.classList.add('dark');
        localStorage.setItem('theme', 'dark');
        console.log('Switched to DARK');
      }
      
      console.log('After - classList:', root.classList.toString());
      console.log('After - contains dark:', root.classList.contains('dark'));
      console.log('=== MINIMAL TOGGLE END ===');
      
      updateStatus();
    }

    function resetThemeMinimal() {
      console.log('=== MINIMAL RESET ===');
      localStorage.removeItem('theme');
      document.documentElement.classList.remove('dark');
      document.documentElement.className = '';
      console.log('Reset complete');
      updateStatus();
    }

    // Initialize
    const saved = localStorage.getItem('theme');
    if (saved === 'dark') {
      document.documentElement.classList.add('dark');
    }
    
    updateStatus();
    console.log('Minimal test page loaded');
  </script>
</body>
</html>
